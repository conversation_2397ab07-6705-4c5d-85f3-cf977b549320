import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AuthService, UserProfile } from '../../../../core/services/auth.service';
import { ModalService } from '../../../../core/services/modal.service';

@Component({
  selector: 'app-profile-settings',
  standalone: false,
  templateUrl: './profile-settings.component.html',
  styleUrl: './profile-settings.component.css'
})
export class ProfileSettingsComponent implements OnInit {
  userProfile: UserProfile | null = null;
  isLoading = true;
  errorMessage = '';

  phoneForm: FormGroup;
  passwordForm: FormGroup;
  isEditingPhone = false;
  isEditingPassword = false;
  savingPhone = false;
  savingPassword = false;

  constructor(
    private authService: AuthService,
    private modalService: ModalService,
    private fb: FormBuilder
  ) {
    this.phoneForm = this.fb.group({
      phone: ['']
    });

    this.passwordForm = this.fb.group({
      old_password: ['', Validators.required],
      new_password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  ngOnInit(): void {
    this.loadUserProfile();
  }

  loadUserProfile(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.authService.getUserProfile().subscribe({
      next: (profile: UserProfile) => {
        this.userProfile = profile;
        this.populatePhoneForm();
        this.isLoading = false;
      },
      error: (error: any) => {
        this.errorMessage = error.message || 'Не удалось загрузить профиль';
        this.isLoading = false;
      }
    });
  }

  populatePhoneForm(): void {
    if (this.userProfile) {
      this.phoneForm.patchValue({
        phone: this.userProfile.phone || ''
      });
    }
  }

  onRefreshProfile(): void {
    this.loadUserProfile();
  }

  verifyToken(): void {
    this.authService.verifyToken().subscribe({
      next: (response) => {
        console.log('Token verified:', response);
        this.modalService.success('Успех', 'Токен действителен!');
      },
      error: (error) => {
        console.error('Token verification failed:', error);
        this.modalService.error('Ошибка', 'Проверка токена не удалась: ' + error.message);
      }
    });
  }

  onLogout(): void {
    this.authService.logout();
  }

  togglePhoneEdit(): void {
    this.isEditingPhone = !this.isEditingPhone;
    if (!this.isEditingPhone) {
      // Reset form when canceling edit
      this.populatePhoneForm();
    }
  }

  togglePasswordEdit(): void {
    this.isEditingPassword = !this.isEditingPassword;
    if (!this.isEditingPassword) {
      // Reset form when canceling edit
      this.passwordForm.reset();
    }
  }

  onUpdatePhone(): void {
    if (this.phoneForm.valid) {
      this.savingPhone = true;

      const phoneData = {
        phone: this.phoneForm.value.phone || ''
      };

      this.authService.updatePhone(phoneData).subscribe({
        next: (updatedProfile) => {
          this.userProfile = updatedProfile;
          this.isEditingPhone = false;
          this.savingPhone = false;
          this.modalService.success('Успех', 'Номер телефона успешно обновлен');
        },
        error: (error) => {
          console.error('Failed to update phone:', error);
          this.savingPhone = false;
          let errorMessage = 'Не удалось обновить номер телефона';

          if (error.error && typeof error.error === 'object') {
            const errors = [];
            for (const [field, messages] of Object.entries(error.error)) {
              if (Array.isArray(messages)) {
                errors.push(`${field}: ${messages.join(', ')}`);
              }
            }
            if (errors.length > 0) {
              errorMessage += ':\n' + errors.join('\n');
            }
          } else if (error.message) {
            errorMessage += ': ' + error.message;
          }

          this.modalService.error('Ошибка', errorMessage);
        }
      });
    }
  }

  onChangePassword(): void {
    if (this.passwordForm.valid) {
      this.savingPassword = true;

      const passwordData = {
        old_password: this.passwordForm.value.old_password,
        new_password: this.passwordForm.value.new_password
      };

      this.authService.changePassword(passwordData).subscribe({
        next: (response) => {
          this.isEditingPassword = false;
          this.savingPassword = false;
          this.passwordForm.reset();
          this.modalService.success('Успех', 'Пароль успешно изменен');
        },
        error: (error) => {
          console.error('Failed to change password:', error);
          this.savingPassword = false;
          let errorMessage = 'Не удалось изменить пароль';

          if (error.error && error.error.error) {
            errorMessage = error.error.error;
          } else if (error.message) {
            errorMessage += ': ' + error.message;
          }

          this.modalService.error('Ошибка', errorMessage);
        }
      });
    }
  }
}
