export interface GamePackage {
  id: number;
  name: string;
  description: string;
  benefit_1: string;
  benefit_2: string;
  benefit_3: string;
  price: string;
  duration_days: number;
  games: GamePackageGame[];
  max_selectable_games: number;
  created_at?: string;
}

export interface GamePackageGame {
  id: number;
  title: string;
}

export interface GamePackageListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: GamePackage[];
}

export interface CreateGamePackageRequest {
  name: string;
  description: string;
  benefit_1?: string;
  benefit_2?: string;
  benefit_3?: string;
  price: string;
  duration_days: number;
  game_ids: number[];
  max_selectable_games: number;
}

export interface UpdateGamePackageRequest extends Partial<CreateGamePackageRequest> {}

export interface GamePackageFilters {
  ordering?: string;
  search?: string;
}

export interface GamePackageError {
  name?: string[];
  description?: string[];
  benefit_1?: string[];
  benefit_2?: string[];
  benefit_3?: string[];
  price?: string[];
  duration_days?: string[];
  game_ids?: string[];
  max_selectable_games?: string[];
  non_field_errors?: string[];
}
