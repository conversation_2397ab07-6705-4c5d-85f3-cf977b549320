import { Component, Input, Output, EventEmitter } from '@angular/core';
import { GamePackage } from '../../../core/models/game-package.model';

@Component({
  selector: 'app-tarrifs-card',
  standalone: false,
  templateUrl: './tarrifs-card.html',
  styleUrl: './tarrifs-card.css'
})
export class TarrifsCard {
  @Input() package: GamePackage | null = null;
  @Input() isAuthenticated: boolean = false;
  @Output() addToCart = new EventEmitter<GamePackage>();
  @Output() viewDetails = new EventEmitter<GamePackage>();

  onAddToCart(): void {
    if (this.package) {
      this.addToCart.emit(this.package);
    }
  }

  onViewDetails(): void {
    if (this.package) {
      this.viewDetails.emit(this.package);
    }
  }

  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    return numPrice.toLocaleString('ru-RU') + ' ₸';
  }
}
