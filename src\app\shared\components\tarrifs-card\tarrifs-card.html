<div *ngIf="package" class="relative w-full max-w-sm mx-auto bg-cover bg-center bg-no-repeat rounded-2xl p-6 md:p-8 flex flex-col justify-between h-auto min-h-[500px] glow-on-hover transition-all duration-300 hover:scale-105 cursor-pointer"
     style="background-image: url('assets/images/rectangle.png');"
     (click)="onViewDetails()">

  <!-- Overlay for better text readability -->
  <div class="absolute inset-0 bg-black/20 rounded-2xl"></div>

  <div class="relative z-10 text-white flex flex-col justify-between h-full">
    <!-- Plan Details -->
    <div class="space-y-6">
      <div class="text-center">
        <h3 class="font-bold text-2xl md:text-3xl mb-2">{{ package.name }}</h3>
        <p class="text-lg md:text-xl text-gray-200">{{ package.description }}</p>
      </div>

      <!-- Features List -->
      <div class="space-y-4">
        <div *ngIf="package.benefit_1" class="flex items-start gap-3">
          <img src="/assets/icons/check.svg" alt="check" class="w-5 h-5 mt-0.5 flex-shrink-0">
          <span class="text-base leading-relaxed">{{ package.benefit_1 }}</span>
        </div>
        <div *ngIf="package.benefit_2" class="flex items-start gap-3">
          <img src="/assets/icons/check.svg" alt="check" class="w-5 h-5 mt-0.5 flex-shrink-0">
          <span class="text-base leading-relaxed">{{ package.benefit_2 }}</span>
        </div>
        <div *ngIf="package.benefit_3" class="flex items-start gap-3">
          <img src="/assets/icons/check.svg" alt="check" class="w-5 h-5 mt-0.5 flex-shrink-0">
          <span class="text-base leading-relaxed">{{ package.benefit_3 }}</span>
        </div>
        <div class="flex items-start gap-3">
          <img src="/assets/icons/check.svg" alt="check" class="w-5 h-5 mt-0.5 flex-shrink-0">
          <span class="text-base leading-relaxed">{{ package.duration_days }} дней доступа</span>
        </div>
        <div class="flex items-start gap-3">
          <img src="/assets/icons/check.svg" alt="check" class="w-5 h-5 mt-0.5 flex-shrink-0">
          <span class="text-base leading-relaxed">До {{ package.max_selectable_games }} игр</span>
        </div>
      </div>
    </div>

    <!-- Pricing and Action -->
    <div class="mt-8 space-y-6">
      <div class="text-center">
        <div class="text-3xl md:text-4xl font-bold mb-1">{{ formatPrice(package.price) }}</div>
        <div class="text-lg text-gray-300">за {{ package.duration_days }} дней</div>
      </div>

      <button
        *ngIf="isAuthenticated"
        (click)="onAddToCart(); $event.stopPropagation()"
        class="w-full bg-[#22AAA8] hover:bg-[#1e9896] py-3 px-4 font-bold rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl"
        style="box-shadow: 0 20px 40px 0 rgba(34, 170, 168, 0.2);">
        В корзину
      </button>

      <button
        *ngIf="!isAuthenticated"
        (click)="$event.stopPropagation()"
        class="w-full bg-gray-600 py-3 px-4 font-bold rounded-lg cursor-not-allowed opacity-75"
        disabled>
        Войдите для покупки
      </button>
    </div>
  </div>
</div>

<!-- Fallback for when no package is provided -->
<div *ngIf="!package" class="relative w-full max-w-sm mx-auto bg-cover bg-center bg-no-repeat rounded-2xl p-6 md:p-8 flex flex-col justify-between h-auto min-h-[500px] glow-on-hover transition-all duration-300 hover:scale-105"
     style="background-image: url('assets/images/rectangle.png');">

  <!-- Overlay for better text readability -->
  <div class="absolute inset-0 bg-black/20 rounded-2xl"></div>

  <div class="relative z-10 text-white flex flex-col justify-between h-full">
    <!-- Plan Details -->
    <div class="space-y-6">
      <div class="text-center">
        <h3 class="font-bold text-2xl md:text-3xl mb-2">BASIC</h3>
        <p class="text-lg md:text-xl text-gray-200">Разовые покупки</p>
      </div>

      <!-- Features List -->
      <div class="space-y-4">
        <div class="flex items-start gap-3">
          <img src="/assets/icons/check.svg" alt="check" class="w-5 h-5 mt-0.5 flex-shrink-0">
          <span class="text-base leading-relaxed">2 ключа на игру</span>
        </div>
        <div class="flex items-start gap-3">
          <img src="/assets/icons/check.svg" alt="check" class="w-5 h-5 mt-0.5 flex-shrink-0">
          <span class="text-base leading-relaxed">Бесплатные устройства на купленную игру</span>
        </div>
      </div>
    </div>

    <!-- Pricing and Action -->
    <div class="mt-8 space-y-6">
      <div class="text-center">
        <div class="text-3xl md:text-4xl font-bold mb-1">20 000 ₸</div>
        <div class="text-lg text-gray-300">за 1 игру</div>
      </div>

      <button class="w-full bg-[#22AAA8] hover:bg-[#1e9896] py-3 px-4 font-bold rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl"
              style="box-shadow: 0 20px 40px 0 rgba(34, 170, 168, 0.2);">
        Купить
      </button>
    </div>
  </div>
</div>
