import { Component, OnInit, OnChanges, Input, Output, EventEmitter } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { GameService } from '../../../../core/services/game.service';
import { GameFileService } from '../../../../core/services/game-file.service';
import { ModalService } from '../../../../core/services/modal.service';
import { Game, UpdateGameRequest, GameFile, CreateGameFileRequest, GameFileError } from '../../../../core/models/game.model';

@Component({
  selector: 'app-game-detail',
  standalone: false,
  templateUrl: './game-detail.component.html',
  styleUrls: ['./game-detail.component.css']
})
export class GameDetailComponent implements OnInit, OnChanges {
  @Input() gameId: number | null = null;
  @Input() isVisible = false;
  @Output() close = new EventEmitter<void>();
  @Output() gameUpdated = new EventEmitter<Game>();
  @Output() gameDeleted = new EventEmitter<number>();

  game: Game | null = null;
  loading = false;
  error = '';

  // Edit mode
  isEditing = false;
  editLoading = false;
  editError = '';
  editGame: UpdateGameRequest = {};
  selectedCoverImage: File | null = null;

  // Gallery images
  selectedGalleryFiles: File[] = [];
  galleryUploadProgress: { [key: string]: number } = {};
  galleryUploadLoading = false;

  // Delete confirmation
  showDeleteConfirm = false;
  deleteLoading = false;

  // Game files management
  gameFiles: GameFile[] = [];
  gameFilesLoading = false;
  gameFilesError = '';
  showGameFilesSection = false;

  // Add game file form
  showAddGameFileForm = false;
  addGameFileLoading = false;
  addGameFileError = '';
  selectedGameFile: File | null = null;
  newGameFile: CreateGameFileRequest = {
    game: 0,
    file: null as any,
    platform: 'windows'
  };

  // Platform options
  platformOptions = [
    { value: 'windows', label: 'Windows' },
    { value: 'mac', label: 'macOS' },
    { value: 'linux', label: 'Linux' },
    { value: 'android', label: 'Android' },
    { value: 'ios', label: 'iOS' },
    { value: 'web', label: 'Web' }
  ];

  constructor(
    private gameService: GameService,
    private gameFileService: GameFileService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    if (this.gameId && this.isVisible) {
      this.loadGame();
    }
  }

  ngOnChanges(): void {
    if (this.gameId && this.isVisible && !this.game) {
      this.loadGame();
    }
  }

  loadGame(): void {
    if (!this.gameId) return;

    this.loading = true;
    this.error = '';

    this.gameService.getGame(this.gameId).subscribe({
      next: (game) => {
        this.game = game;
        this.initializeEditForm();
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Failed to load game details';
        this.loading = false;
      }
    });
  }

  initializeEditForm(): void {
    if (!this.game) return;

    this.editGame = {
      title: this.game.title,
      subtitle: this.game.subtitle || '',
      description: this.game.description,
      how_to_play: this.game.how_to_play || '',
      target_audience: this.game.target_audience || '',
      requires_device: this.game.requires_device,
      price: this.game.price,
      trial_available: this.game.trial_available,
      system_requirements: this.game.system_requirements || '',
      required_equipment: this.game.required_equipment || '',
      game_code: this.game.game_code || ''
    };
  }

  toggleEdit(): void {
    this.isEditing = !this.isEditing;
    if (!this.isEditing) {
      this.initializeEditForm();
      this.selectedCoverImage = null;
      this.selectedGalleryFiles = [];
      this.galleryUploadProgress = {};
      this.editError = '';
    }
  }

  onCoverImageSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        this.editError = 'Пожалуйста, выберите файл изображения';
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        this.editError = 'Размер файла не должен превышать 5MB';
        return;
      }

      this.selectedCoverImage = file;
      this.editError = '';
    }
  }

  onEditGameCodeInput(event: any): void {
    const input = event.target;
    const value = input.value.toUpperCase().slice(0, 6); // Convert to uppercase and limit to 6 characters
    this.editGame.game_code = value;
    input.value = value; // Update the input field display
  }

  onGalleryFilesSelected(event: any): void {
    const files = Array.from(event.target.files) as File[];
    if (files.length === 0) return;

    // Validate each file
    for (const file of files) {
      // Validate file type (images and videos)
      if (!file.type.startsWith('image/') && !file.type.startsWith('video/')) {
        this.editError = 'Пожалуйста, выберите только файлы изображений или видео';
        return;
      }

      // Validate file size (max 10MB for videos, 5MB for images)
      const maxSize = file.type.startsWith('video/') ? 10 * 1024 * 1024 : 5 * 1024 * 1024;
      if (file.size > maxSize) {
        const maxSizeText = file.type.startsWith('video/') ? '10MB' : '5MB';
        this.editError = `Размер файла ${file.name} не должен превышать ${maxSizeText}`;
        return;
      }
    }

    // Add files to the selection
    this.selectedGalleryFiles = [...this.selectedGalleryFiles, ...files];
    this.editError = '';
  }

  removeGalleryFile(index: number): void {
    this.selectedGalleryFiles.splice(index, 1);
  }

  async removeExistingGalleryImage(galleryItemId: number): Promise<void> {
    try {
      await firstValueFrom(this.gameService.deleteGalleryItem(galleryItemId));
      // Reload game data to refresh gallery
      this.loadGame();
    } catch (error) {
      console.error('Error deleting gallery item:', error);
      this.editError = 'Ошибка при удалении изображения из галереи';
    }
  }

  async uploadGalleryFiles(gameId: number): Promise<void> {
    if (this.selectedGalleryFiles.length === 0) {
      return;
    }

    this.galleryUploadLoading = true;

    try {
      for (let i = 0; i < this.selectedGalleryFiles.length; i++) {
        const file = this.selectedGalleryFiles[i];
        const fileKey = `${file.name}_${i}`;

        this.galleryUploadProgress[fileKey] = 0;

        try {
          await firstValueFrom(this.gameService.addGalleryItem(gameId, file));
          this.galleryUploadProgress[fileKey] = 100;
        } catch (error) {
          console.error(`Error uploading file ${file.name}:`, error);
          throw new Error(`Ошибка загрузки файла ${file.name}`);
        }
      }
    } finally {
      this.galleryUploadLoading = false;
    }
  }

  async saveChanges(): Promise<void> {
    if (!this.game || !this.gameId) return;

    // Validate required fields
    if (!this.editGame.title?.trim()) {
      this.editError = 'Название игры обязательно';
      return;
    }

    if (!this.editGame.description?.trim()) {
      this.editError = 'Описание игры обязательно';
      return;
    }

    if (!this.editGame.price?.trim()) {
      this.editError = 'Цена игры обязательна';
      return;
    }

    // Validate price format
    const priceNum = parseFloat(this.editGame.price);
    if (isNaN(priceNum) || priceNum < 0) {
      this.editError = 'Введите корректную цену (должна быть больше или равна 0)';
      return;
    }

    // Validate game code if provided
    if (this.editGame.game_code && this.editGame.game_code.trim()) {
      const gameCode = this.editGame.game_code.trim().toUpperCase();
      if (gameCode.length > 6) {
        this.editError = 'Код игры не может быть длиннее 6 символов';
        return;
      }
      // Update the game code to ensure it's uppercase
      this.editGame.game_code = gameCode;
    }

    this.editLoading = true;
    this.editError = '';

    // Prepare update data
    const updateData: UpdateGameRequest = {
      ...this.editGame,
      price: priceNum.toFixed(2)
    };

    // Ensure game_code is uppercase if provided
    if (updateData.game_code) {
      updateData.game_code = updateData.game_code.toUpperCase();
    }

    // Add cover image if selected
    if (this.selectedCoverImage) {
      updateData.cover_image = this.selectedCoverImage;
    }

    this.gameService.updateGame(this.gameId, updateData).subscribe({
      next: (updatedGame) => {
        // Game updated successfully, now handle gallery upload separately
        if (this.selectedGalleryFiles.length > 0) {
          this.uploadGalleryForUpdatedGame(updatedGame);
        } else {
          // No gallery files, complete the process
          this.completeGameUpdate(updatedGame);
        }
      },
      error: (error) => {
        this.editLoading = false;

        // Handle validation errors
        if (error.error && typeof error.error === 'object') {
          const errors = error.error;
          if (errors.title) {
            this.editError = 'Название: ' + errors.title.join(', ');
          } else if (errors.description) {
            this.editError = 'Описание: ' + errors.description.join(', ');
          } else if (errors.price) {
            this.editError = 'Цена: ' + errors.price.join(', ');
          } else if (errors.cover_image) {
            this.editError = 'Изображение: ' + errors.cover_image.join(', ');
          } else if (errors.non_field_errors) {
            this.editError = errors.non_field_errors.join(', ');
          } else {
            this.editError = 'Ошибка при обновлении игры';
          }
        } else {
          this.editError = error.message || 'Ошибка при обновлении игры';
        }
      }
    });
  }

  confirmDelete(): void {
    this.showDeleteConfirm = true;
  }

  cancelDelete(): void {
    this.showDeleteConfirm = false;
  }

  deleteGame(): void {
    if (!this.gameId) return;

    this.deleteLoading = true;

    this.gameService.deleteGame(this.gameId).subscribe({
      next: () => {
        this.deleteLoading = false;
        this.showDeleteConfirm = false;
        this.gameDeleted.emit(this.gameId!);
        this.closeModal();
        this.modalService.success('Успех', 'Игра успешно удалена!');
      },
      error: (error) => {
        this.deleteLoading = false;
        this.modalService.error('Ошибка удаления', 'Ошибка при удалении игры: ' + error.message);
      }
    });
  }

  closeModal(): void {
    this.isEditing = false;
    this.showDeleteConfirm = false;
    this.editError = '';
    this.selectedCoverImage = null;
    this.selectedGalleryFiles = [];
    this.galleryUploadProgress = {};
    this.close.emit();
  }

  formatPrice(price: string): string {
    const num = parseFloat(price);
    return num.toLocaleString('ru-RU', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  }

  private async uploadGalleryForUpdatedGame(updatedGame: any): Promise<void> {
    try {
      // Validate gallery files before uploading
      for (const file of this.selectedGalleryFiles) {
        // Validate file type (images and videos)
        if (!file.type.startsWith('image/') && !file.type.startsWith('video/')) {
          throw new Error(`Неподдерживаемый формат файла: ${file.name}. Поддерживаются только изображения и видео.`);
        }

        // Validate file size (max 10MB for videos, 5MB for images)
        const maxSize = file.type.startsWith('video/') ? 10 * 1024 * 1024 : 5 * 1024 * 1024;
        if (file.size > maxSize) {
          const maxSizeText = file.type.startsWith('video/') ? '10MB' : '5MB';
          throw new Error(`Файл ${file.name} слишком большой. Максимальный размер: ${maxSizeText}`);
        }
      }

      // Upload gallery files
      await this.uploadGalleryFiles(updatedGame.id);

      // Success - complete the process
      this.completeGameUpdate(updatedGame);

    } catch (galleryError: any) {
      // Gallery upload failed, but game was already updated
      this.editLoading = false;
      this.editError = galleryError.message || 'Ошибка при загрузке файлов галереи';

      // Show warning that game was updated but gallery failed
      this.modalService.error('Частичное обновление', `Игра обновлена успешно, но не удалось загрузить галерею: ${this.editError}`);

      // Still complete the update process (without gallery)
      this.completeGameUpdate(updatedGame);
    }
  }

  private completeGameUpdate(updatedGame: any): void {
    // Reload game data to get updated gallery
    this.loadGame();

    this.isEditing = false;
    this.editLoading = false;
    this.selectedCoverImage = null;
    this.selectedGalleryFiles = [];
    this.galleryUploadProgress = {};
    this.gameUpdated.emit(updatedGame);
    this.modalService.success('Успех', 'Игра успешно обновлена!');
  }

  // Game Files Management Methods
  toggleGameFilesSection(): void {
    this.showGameFilesSection = !this.showGameFilesSection;
    if (this.showGameFilesSection && this.gameFiles.length === 0) {
      this.loadGameFiles();
    }
  }

  loadGameFiles(): void {
    if (!this.game) return;

    this.gameFilesLoading = true;
    this.gameFilesError = '';

    this.gameFileService.getGameFilesByGame(this.game.id).subscribe({
      next: (response) => {
        this.gameFiles = response.results;
        this.gameFilesLoading = false;
      },
      error: (error) => {
        this.gameFilesError = error.message || 'Failed to load game files';
        this.gameFilesLoading = false;
      }
    });
  }

  toggleAddGameFileForm(): void {
    this.showAddGameFileForm = !this.showAddGameFileForm;
    if (this.showAddGameFileForm) {
      this.resetAddGameFileForm();
    }
  }

  private resetAddGameFileForm(): void {
    this.newGameFile = {
      game: this.game?.id || 0,
      file: null as any,
      platform: 'windows'
    };
    this.selectedGameFile = null;
    this.addGameFileError = '';
  }

  onGameFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file size (max 500MB)
      if (file.size > 500 * 1024 * 1024) {
        this.addGameFileError = 'Размер файла не должен превышать 500MB';
        return;
      }

      this.selectedGameFile = file;
      this.newGameFile.file = file;
      this.addGameFileError = '';
    }
  }

  onAddGameFile(): void {
    if (!this.selectedGameFile) {
      this.addGameFileError = 'Пожалуйста, выберите файл';
      return;
    }

    this.addGameFileLoading = true;
    this.addGameFileError = '';

    this.gameFileService.createGameFile(this.newGameFile).subscribe({
      next: (gameFile) => {
        this.gameFiles.unshift(gameFile);
        this.showAddGameFileForm = false;
        this.addGameFileLoading = false;
        this.modalService.success('Успех', 'Файл игры успешно добавлен');
      },
      error: (error) => {
        this.addGameFileLoading = false;
        if (error && typeof error === 'object') {
          const gameFileError = error as GameFileError;
          if (gameFileError.file) {
            this.addGameFileError = 'Файл: ' + gameFileError.file.join(', ');
          } else if (gameFileError.platform) {
            this.addGameFileError = 'Платформа: ' + gameFileError.platform.join(', ');
          } else if (gameFileError.non_field_errors) {
            this.addGameFileError = gameFileError.non_field_errors.join(', ');
          } else {
            this.addGameFileError = 'Произошла ошибка при добавлении файла';
          }
        } else {
          this.addGameFileError = error.message || 'Произошла ошибка при добавлении файла';
        }
      }
    });
  }

  downloadGameFile(gameFile: GameFile): void {
    this.gameFileService.triggerFileDownload(gameFile.id, gameFile.file_name).subscribe({
      next: () => {
        // Download started successfully
      },
      error: (error) => {
        this.modalService.error('Ошибка', 'Не удалось скачать файл: ' + error.message);
      }
    });
  }

  deleteGameFile(gameFile: GameFile): void {
    this.modalService.confirm(
      'Удаление файла игры',
      `Вы уверены, что хотите удалить файл "${gameFile.file_name}"?`,
      'Удалить',
      'Отмена'
    ).then(confirmed => {
      if (confirmed) {
        this.gameFileService.deleteGameFile(gameFile.id).subscribe({
          next: () => {
            this.gameFiles = this.gameFiles.filter(gf => gf.id !== gameFile.id);
            this.modalService.success('Успех', 'Файл игры успешно удален');
          },
          error: (error) => {
            this.modalService.error('Ошибка', 'Не удалось удалить файл игры: ' + error.message);
          }
        });
      }
    });
  }

  // Utility methods for game files
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getPlatformLabel(platform: string): string {
    const option = this.platformOptions.find(p => p.value === platform);
    return option ? option.label : platform;
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}
