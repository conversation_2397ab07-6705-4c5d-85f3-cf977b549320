import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { of, throwError } from 'rxjs';
import { GameFilesManagementComponent } from './game-files-management.component';
import { GameFileService } from '../../../../core/services/game-file.service';
import { GameService } from '../../../../core/services/game.service';
import { ModalService } from '../../../../core/services/modal.service';
import { GameFile, Game, GameFileListResponse, CreateGameFileRequest } from '../../../../core/models/game.model';

describe('GameFilesManagementComponent', () => {
  let component: GameFilesManagementComponent;
  let fixture: ComponentFixture<GameFilesManagementComponent>;
  let gameFileService: jasmine.SpyObj<GameFileService>;
  let gameService: jasmine.SpyObj<GameService>;
  let modalService: jasmine.SpyObj<ModalService>;

  const mockGameFiles: GameFile[] = [
    {
      id: 1,
      game: 1,
      file: '/media/game_files/1/windows/MyGame.exe',
      file_name: 'MyGame.exe',
      file_size: 1024000,
      platform: 'windows',
      version: '1.0',
      description: 'Windows executable',
      is_active: true,
      download_url: 'http://localhost:8000/api/game-files/1/download/',
      uploaded_at: '2025-07-20T23:00:00Z',
      updated_at: '2025-07-20T23:00:00Z'
    }
  ];

  const mockGames: Game[] = [
    {
      id: 1,
      title: 'Test Game',
      description: 'Test Description',
      requires_device: true,
      price: '1990.00',
      trial_available: true,
      cover_image: '/media/test.jpg',
      created_at: '2025-01-01T00:00:00Z'
    }
  ];

  const mockGameFileListResponse: GameFileListResponse = {
    count: 1,
    next: null,
    previous: null,
    results: mockGameFiles
  };

  beforeEach(async () => {
    const gameFileServiceSpy = jasmine.createSpyObj('GameFileService', [
      'getGameFiles', 'createGameFile', 'deleteGameFile', 'triggerFileDownload'
    ]);
    const gameServiceSpy = jasmine.createSpyObj('GameService', ['getGames']);
    const modalServiceSpy = jasmine.createSpyObj('ModalService', ['success', 'error', 'confirm']);

    await TestBed.configureTestingModule({
      declarations: [GameFilesManagementComponent],
      imports: [FormsModule],
      providers: [
        { provide: GameFileService, useValue: gameFileServiceSpy },
        { provide: GameService, useValue: gameServiceSpy },
        { provide: ModalService, useValue: modalServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(GameFilesManagementComponent);
    component = fixture.componentInstance;
    gameFileService = TestBed.inject(GameFileService) as jasmine.SpyObj<GameFileService>;
    gameService = TestBed.inject(GameService) as jasmine.SpyObj<GameService>;
    modalService = TestBed.inject(ModalService) as jasmine.SpyObj<ModalService>;

    // Setup default spy returns
    gameFileService.getGameFiles.and.returnValue(of(mockGameFileListResponse));
    gameService.getGames.and.returnValue(of({ count: 1, next: null, previous: null, results: mockGames }));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should load game files and games on init', () => {
      component.ngOnInit();

      expect(gameFileService.getGameFiles).toHaveBeenCalled();
      expect(gameService.getGames).toHaveBeenCalled();
      expect(component.gameFiles).toEqual(mockGameFiles);
      expect(component.games).toEqual(mockGames);
    });
  });

  describe('loadGameFiles', () => {
    it('should load game files successfully', () => {
      component.loadGameFiles();

      expect(component.gameFilesLoading).toBeFalse();
      expect(component.gameFiles).toEqual(mockGameFiles);
      expect(component.totalGameFiles).toBe(1);
      expect(component.gameFilesError).toBe('');
    });

    it('should handle error when loading game files', () => {
      gameFileService.getGameFiles.and.returnValue(throwError(() => new Error('Test error')));

      component.loadGameFiles();

      expect(component.gameFilesLoading).toBeFalse();
      expect(component.gameFilesError).toBe('Test error');
    });
  });

  describe('file selection and validation', () => {
    it('should select valid file', () => {
      const file = new File(['test'], 'test.exe', { type: 'application/octet-stream' });
      const event = { target: { files: [file] } };

      component.onFileSelected(event);

      expect(component.selectedFile).toBe(file);
      expect(component.newGameFile.file).toBe(file);
      expect(component.addGameFileError).toBe('');
    });

    it('should reject file that is too large', () => {
      const largeFile = new File(['x'.repeat(501 * 1024 * 1024)], 'large.exe');
      const event = { target: { files: [largeFile] } };

      component.onFileSelected(event);

      expect(component.selectedFile).toBeNull();
      expect(component.addGameFileError).toBe('Размер файла не должен превышать 500MB');
    });
  });

  describe('toggleAddForm', () => {
    it('should toggle add form visibility', () => {
      expect(component.showAddForm).toBeFalse();

      component.toggleAddForm();
      expect(component.showAddForm).toBeTrue();

      component.toggleAddForm();
      expect(component.showAddForm).toBeFalse();
    });

    it('should reset form when showing', () => {
      component.newGameFile.game = 5;
      component.selectedFile = new File(['test'], 'test.exe');
      component.addGameFileError = 'Some error';

      component.toggleAddForm();

      expect(component.newGameFile.game).toBe(0);
      expect(component.selectedFile).toBeNull();
      expect(component.addGameFileError).toBe('');
    });
  });

  describe('onAddGameFile', () => {
    beforeEach(() => {
      component.newGameFile.game = 1;
      component.selectedFile = new File(['test'], 'test.exe');
      component.newGameFile.file = component.selectedFile;
    });

    it('should add game file successfully', () => {
      const newGameFile: GameFile = {
        id: 2,
        game: 1,
        file: '/media/game_files/1/windows/test.exe',
        file_name: 'test.exe',
        file_size: 4,
        platform: 'windows',
        version: '1.0',
        description: '',
        is_active: true,
        download_url: 'http://localhost:8000/api/game-files/2/download/',
        uploaded_at: '2025-07-20T23:00:00Z',
        updated_at: '2025-07-20T23:00:00Z'
      };

      gameFileService.createGameFile.and.returnValue(of(newGameFile));

      component.onAddGameFile();

      expect(gameFileService.createGameFile).toHaveBeenCalledWith(component.newGameFile);
      expect(component.gameFiles).toContain(newGameFile);
      expect(component.showAddForm).toBeFalse();
      expect(component.addGameFileLoading).toBeFalse();
      expect(modalService.success).toHaveBeenCalledWith('Успех', 'Файл игры успешно добавлен');
    });

    it('should show error when game not selected', () => {
      component.newGameFile.game = 0;

      component.onAddGameFile();

      expect(component.addGameFileError).toBe('Пожалуйста, выберите игру и файл');
      expect(gameFileService.createGameFile).not.toHaveBeenCalled();
    });

    it('should show error when file not selected', () => {
      component.selectedFile = null;

      component.onAddGameFile();

      expect(component.addGameFileError).toBe('Пожалуйста, выберите игру и файл');
      expect(gameFileService.createGameFile).not.toHaveBeenCalled();
    });

    it('should handle API error', () => {
      const error = { file: ['This field is required.'] };
      gameFileService.createGameFile.and.returnValue(throwError(() => error));

      component.onAddGameFile();

      expect(component.addGameFileLoading).toBeFalse();
      expect(component.addGameFileError).toBe('Файл: This field is required.');
    });
  });

  describe('downloadGameFile', () => {
    it('should trigger file download', () => {
      gameFileService.triggerFileDownload.and.returnValue(of(undefined));

      component.downloadGameFile(mockGameFiles[0]);

      expect(gameFileService.triggerFileDownload).toHaveBeenCalledWith(1, 'MyGame.exe');
    });

    it('should show error on download failure', () => {
      gameFileService.triggerFileDownload.and.returnValue(throwError(() => new Error('Download failed')));

      component.downloadGameFile(mockGameFiles[0]);

      expect(modalService.error).toHaveBeenCalledWith('Ошибка', 'Не удалось скачать файл: Download failed');
    });
  });

  describe('deleteGameFile', () => {
    it('should delete game file after confirmation', async () => {
      modalService.confirm.and.returnValue(Promise.resolve(true));
      gameFileService.deleteGameFile.and.returnValue(of(undefined));
      component.gameFiles = [...mockGameFiles];

      await component.deleteGameFile(mockGameFiles[0]);

      expect(modalService.confirm).toHaveBeenCalled();
      expect(gameFileService.deleteGameFile).toHaveBeenCalledWith(1);
      expect(component.gameFiles).toEqual([]);
      expect(modalService.success).toHaveBeenCalledWith('Успех', 'Файл игры успешно удален');
    });

    it('should not delete if user cancels', async () => {
      modalService.confirm.and.returnValue(Promise.resolve(false));
      component.gameFiles = [...mockGameFiles];

      await component.deleteGameFile(mockGameFiles[0]);

      expect(gameFileService.deleteGameFile).not.toHaveBeenCalled();
      expect(component.gameFiles).toEqual(mockGameFiles);
    });

    it('should show error on delete failure', async () => {
      modalService.confirm.and.returnValue(Promise.resolve(true));
      gameFileService.deleteGameFile.and.returnValue(throwError(() => new Error('Delete failed')));

      await component.deleteGameFile(mockGameFiles[0]);

      expect(modalService.error).toHaveBeenCalledWith('Ошибка', 'Не удалось удалить файл игры: Delete failed');
    });
  });

  describe('utility methods', () => {
    it('should format file size correctly', () => {
      expect(component.formatFileSize(0)).toBe('0 Bytes');
      expect(component.formatFileSize(1024)).toBe('1 KB');
      expect(component.formatFileSize(1048576)).toBe('1 MB');
      expect(component.formatFileSize(1073741824)).toBe('1 GB');
    });

    it('should get platform label', () => {
      expect(component.getPlatformLabel('windows')).toBe('Windows');
      expect(component.getPlatformLabel('mac')).toBe('macOS');
      expect(component.getPlatformLabel('unknown')).toBe('unknown');
    });

    it('should format date', () => {
      const dateString = '2025-07-20T23:00:00Z';
      const result = component.formatDate(dateString);
      expect(result).toContain('2025');
    });

    it('should get game title', () => {
      component.games = mockGames;
      expect(component.getGameTitle(1)).toBe('Test Game');
      expect(component.getGameTitle(999)).toBe('Игра #999');
    });
  });

  describe('filtering and search', () => {
    it('should build filters correctly', () => {
      component.searchTerm = 'test';
      component.selectedGame = 1;
      component.selectedPlatform = 'windows';
      component.selectedActiveStatus = 'active';
      component.sortBy = '-id';

      const filters = (component as any).buildFilters();

      expect(filters.search).toBe('test');
      expect(filters.game).toBe(1);
      expect(filters.platform).toBe('windows');
      expect(filters.is_active).toBe(true);
      expect(filters.ordering).toBe('-id');
    });

    it('should handle search changes', () => {
      spyOn(component, 'loadGameFiles');
      component.searchTerm = 'new search';

      component.onSearchChange();

      // Search is debounced, so we need to wait
      setTimeout(() => {
        expect(component.currentPage).toBe(1);
        expect(component.loadGameFiles).toHaveBeenCalled();
      }, 350);
    });
  });

  describe('pagination', () => {
    it('should calculate total pages correctly', () => {
      component.totalGameFiles = 25;
      component.pageSize = 10;

      expect(component.totalPages).toBe(3);
    });

    it('should generate page numbers correctly', () => {
      component.totalGameFiles = 100;
      component.pageSize = 10;
      component.currentPage = 5;

      const pages = component.pages;
      expect(pages.length).toBeLessThanOrEqual(10);
      expect(pages).toContain(5);
    });

    it('should change page', () => {
      spyOn(component, 'loadGameFiles');

      component.onPageChange(2);

      expect(component.currentPage).toBe(2);
      expect(component.loadGameFiles).toHaveBeenCalled();
    });
  });
});
