<!-- Game Detail Component -->
<div class="space-y-6">
  
  <!-- Header with <PERSON> -->
  <div class="flex items-center justify-between mb-6">
    <div>
      <button
        (click)="closeModal()"
        class="flex items-center gap-2 text-gray-300 hover:text-white transition-colors text-sm mb-2"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        {{ getBackButtonText() }}
      </button>
      <h1 class="text-3xl font-black text-white">
        Детали игры
      </h1>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex justify-center items-center py-12">
    <div class="bg-slate-800/60 backdrop-blur-md border border-slate-600/30 rounded-xl p-6">
      <svg class="animate-spin h-8 w-8 text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-6 text-center">
    <p class="text-red-300 mb-4">{{ error }}</p>
    <button 
      (click)="loadGame()" 
      class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors text-sm"
    >
      Попробовать снова
    </button>
  </div>

  <!-- Game Content -->
  <div *ngIf="game && !loading && !error" class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-6">
    <!-- Game Header -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
      <!-- Cover Image -->
      <div class="lg:col-span-1">
        <div class="aspect-square bg-slate-700/40 border border-slate-600/50 rounded-lg overflow-hidden">
          <img 
            *ngIf="game.cover_image" 
            [src]="game.cover_image" 
            [alt]="game.title"
            class="w-full h-full object-cover">
          <div *ngIf="!game.cover_image" class="w-full h-full flex items-center justify-center text-gray-400">
            <svg class="w-16 h-16" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- Game Info -->
      <div class="lg:col-span-2 space-y-3">
        <div>
          <h1 class="text-2xl font-bold text-white mb-2">{{ game.title }}</h1>
          <p *ngIf="game.subtitle" class="text-lg text-gray-300 mb-3">{{ game.subtitle }}</p>

          <!-- Price and Badges -->
          <div class="flex flex-wrap items-center gap-2 mb-3">
            <span class="text-lg font-bold text-green-400">{{ formatPrice(game.price) }} ₸</span>
            <span *ngIf="game.trial_available" class="px-2 py-1 bg-blue-600 text-white text-xs rounded-full">
              Пробная версия
            </span>
            <span *ngIf="game.requires_device" class="px-2 py-1 bg-orange-600 text-white text-xs rounded-full">
              Требует устройство
            </span>
          </div>

          <!-- Game Status -->
          <div class="mb-4">
            <div *ngIf="canPlay()" class="flex items-center gap-2 text-green-400 text-sm">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              В библиотеке - можно играть
            </div>
            <div *ngIf="needsAccessExtension() && !hasUnactivatedAccess()" class="flex items-center gap-2 text-orange-400 text-sm">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
              Доступ истёк - можно продлить
            </div>
            <div *ngIf="canActivate()" class="flex items-center gap-2 text-yellow-400 text-sm">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 2L13.09 8.26L20 9L15 13.74L16.18 20.02L10 16.77L3.82 20.02L5 13.74L0 9L6.91 8.26L10 2Z" clip-rule="evenodd"></path>
              </svg>
              Доступ на 1 день готов к активации
            </div>
            <div *ngIf="isInCart()" class="flex items-center gap-2 text-blue-400 text-sm">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
              </svg>
              В корзине
            </div>
          </div>

          <!-- Actions -->
          <div class="flex gap-3 items-center">
            <!-- Play Button (has access) -->
            <button
              *ngIf="canPlay()"
              class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors text-sm font-medium"
            >
              Играть
            </button>

            <!-- Buy Game Button (not in library) -->
            <button
              *ngIf="canBuy()"
              (click)="addToCart()"
              class="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-lg transition-all text-sm font-medium"
            >
              В корзину
            </button>

            <!-- Already in Cart Button -->
            <button
              *ngIf="isInCart()"
              disabled
              class="px-4 py-2 bg-gray-600 text-gray-300 rounded-lg cursor-not-allowed text-sm font-medium"
            >
              В корзине
            </button>

            <!-- Activate Game Button -->
            <button
              *ngIf="canActivate()"
              (click)="activateGame()"
              [disabled]="activationLoading"
              class="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors text-sm font-medium flex items-center gap-2"
            >
              <svg *ngIf="activationLoading" class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              <svg *ngIf="!activationLoading" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 2L13.09 8.26L20 9L15 13.74L16.18 20.02L10 16.77L3.82 20.02L5 13.74L0 9L6.91 8.26L10 2Z" clip-rule="evenodd"></path>
              </svg>
              {{ activationLoading ? 'Активация...' : 'Активировать доступ' }}
            </button>

            <!-- Extend Access Button -->
            <button
              *ngIf="needsAccessExtension() && !hasUnactivatedAccess()"
              (click)="addToCart()"
              class="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition-colors text-sm font-medium"
            >
              Продлить доступ
            </button>

            <!-- In Library Button (has access) -->
            <span
              *ngIf="isInLibrary() && hasAccess()"
              class="px-3 py-1 bg-green-600/20 text-green-400 rounded-lg text-xs font-medium border border-green-600/30"
            >
              В библиотеке
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Gallery Section -->
    <div *ngIf="game.gallery_items && game.gallery_items.length > 0" class="mb-6">
      <h3 class="text-lg font-semibold text-white mb-3">Галерея</h3>
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
        <div 
          *ngFor="let item of game.gallery_items; let i = index"
          class="aspect-video bg-slate-700/40 border border-slate-600/50 rounded-lg overflow-hidden cursor-pointer hover:border-slate-500/60 transition-all"
          (click)="openGallery(i)"
        >
          <img 
            [src]="item.file" 
            [alt]="'Gallery image ' + (i + 1)"
            class="w-full h-full object-cover hover:scale-105 transition-transform"
          >
        </div>
      </div>
    </div>

    <!-- Game Description -->
    <div class="space-y-4">
      <div>
        <h3 class="text-lg font-semibold text-white mb-2">Описание</h3>
        <p class="text-gray-300 text-sm leading-relaxed">{{ game.description }}</p>
      </div>

      <!-- How to Play -->
      <div *ngIf="game.how_to_play">
        <h3 class="text-lg font-semibold text-white mb-2">Как играть</h3>
        <p class="text-gray-300 text-sm leading-relaxed">{{ game.how_to_play }}</p>
      </div>

      <!-- Target Audience -->
      <div *ngIf="game.target_audience">
        <h3 class="text-lg font-semibold text-white mb-2">Целевая аудитория</h3>
        <p class="text-gray-300 text-sm leading-relaxed">{{ game.target_audience }}</p>
      </div>

      <!-- System Requirements -->
      <div *ngIf="game.system_requirements">
        <h3 class="text-lg font-semibold text-white mb-2">Системные требования</h3>
        <p class="text-gray-300 text-sm leading-relaxed">{{ game.system_requirements }}</p>
      </div>

      <!-- Required Equipment -->
      <div *ngIf="game.required_equipment">
        <h3 class="text-lg font-semibold text-white mb-2">Необходимое оборудование</h3>
        <p class="text-gray-300 text-sm leading-relaxed">{{ game.required_equipment }}</p>
      </div>

      <!-- Creation Date -->
      <div class="pt-4 border-t border-slate-600/50">
        <p class="text-gray-400 text-xs">
          Добавлено: {{ formatDate(game.created_at) }}
        </p>
      </div>
    </div>
  </div>
</div>

<!-- Gallery Modal -->
<div *ngIf="showGalleryModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90" (click)="closeGallery()">
  <div class="relative max-w-4xl max-h-full p-4" (click)="$event.stopPropagation()">
    <!-- Close Button -->
    <button
      (click)="closeGallery()"
      class="absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </button>

    <!-- Navigation Buttons -->
    <button
      *ngIf="currentImageIndex > 0"
      (click)="prevImage()"
      class="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
      </svg>
    </button>

    <button
      *ngIf="currentImageIndex < galleryImages.length - 1"
      (click)="nextImage()"
      class="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
      </svg>
    </button>

    <!-- Image -->
    <img
      [src]="galleryImages[currentImageIndex]"
      [alt]="'Gallery image ' + (currentImageIndex + 1)"
      class="max-w-full max-h-full object-contain"
    >

    <!-- Image Counter -->
    <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
      {{ currentImageIndex + 1 }} / {{ galleryImages.length }}
    </div>
  </div>
</div>
