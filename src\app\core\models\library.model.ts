import { Game } from './game.model';

export interface LibraryItem {
  id: number;
  game: Game;
  added_at: string;
}

export interface LibraryResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: LibraryItem[];
}

export interface AddToLibraryRequest {
  user_id: number;
  game_id: number;
}

export interface AddToLibraryResponse {
  id: number;
  user_id: number;
  game_id: number;
}

export interface LibraryFilters {
  search?: string;
  ordering?: string;
}

export interface LibraryError {
  user_id?: string[];
  game_id?: string[];
  non_field_errors?: string[];
}
