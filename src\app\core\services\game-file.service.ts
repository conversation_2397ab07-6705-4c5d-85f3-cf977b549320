import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { 
  GameFile, 
  GameFileListResponse, 
  CreateGameFileRequest, 
  UpdateGameFileRequest,
  GameFileFilters 
} from '../models/game.model';

@Injectable({
  providedIn: 'root'
})
export class GameFileService {
  private apiUrl = `${environment.apiUrl}/api/game-files`;

  constructor(private http: HttpClient) {}

  /**
   * Get all game files with optional filtering and pagination
   * @param filters - Optional filters for search, ordering, game, platform, and active status
   * @param page - Page number for pagination
   * @param pageSize - Number of items per page
   */
  getGameFiles(filters?: GameFileFilters, page?: number, pageSize?: number): Observable<GameFileListResponse> {
    let params = new HttpParams();

    if (filters?.search) {
      params = params.set('search', filters.search);
    }

    if (filters?.ordering) {
      params = params.set('ordering', filters.ordering);
    }

    if (filters?.game) {
      params = params.set('game', filters.game.toString());
    }

    if (filters?.platform) {
      params = params.set('platform', filters.platform);
    }

    if (filters?.is_active !== undefined) {
      params = params.set('is_active', filters.is_active.toString());
    }

    if (page) {
      params = params.set('page', page.toString());
    }

    if (pageSize) {
      params = params.set('page_size', pageSize.toString());
    }

    return this.http.get<GameFileListResponse>(`${this.apiUrl}/`, { params }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get a single game file by ID
   * @param id - Game file ID
   */
  getGameFile(id: number): Observable<GameFile> {
    return this.http.get<GameFile>(`${this.apiUrl}/${id}/`).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Create a new game file (admin only)
   * @param gameFileData - Game file data to create
   */
  createGameFile(gameFileData: CreateGameFileRequest): Observable<GameFile> {
    const formData = new FormData();
    formData.append('game', gameFileData.game.toString());
    formData.append('file', gameFileData.file);
    formData.append('platform', gameFileData.platform);
    
    if (gameFileData.version) {
      formData.append('version', gameFileData.version);
    }
    
    if (gameFileData.description) {
      formData.append('description', gameFileData.description);
    }

    return this.http.post<GameFile>(`${this.apiUrl}/`, formData).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Update a game file (admin only)
   * @param id - Game file ID
   * @param gameFileData - Updated game file data
   */
  updateGameFile(id: number, gameFileData: UpdateGameFileRequest): Observable<GameFile> {
    return this.http.patch<GameFile>(`${this.apiUrl}/${id}/`, gameFileData).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Delete a game file (admin only)
   * @param id - Game file ID
   */
  deleteGameFile(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}/`).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Download a game file (requires game access)
   * @param id - Game file ID
   */
  downloadGameFile(id: number): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/${id}/download/`, { 
      responseType: 'blob',
      observe: 'body'
    }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get game files for a specific game
   * @param gameId - Game ID
   */
  getGameFilesByGame(gameId: number): Observable<GameFileListResponse> {
    return this.getGameFiles({ game: gameId });
  }

  /**
   * Trigger file download in browser
   * @param id - Game file ID
   * @param fileName - Name for the downloaded file
   */
  triggerFileDownload(id: number, fileName: string): Observable<void> {
    return new Observable(observer => {
      this.downloadGameFile(id).subscribe({
        next: (blob: Blob) => {
          // Create blob URL and trigger download
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = fileName;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
          observer.next();
          observer.complete();
        },
        error: (error) => {
          observer.error(error);
        }
      });
    });
  }

  /**
   * Handle HTTP errors
   * @param error - HTTP error response
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      if (error.status === 400 && error.error) {
        // Validation errors
        return throwError(() => error.error);
      } else if (error.status === 401) {
        errorMessage = 'Unauthorized access';
      } else if (error.status === 403) {
        errorMessage = 'Access forbidden';
      } else if (error.status === 404) {
        errorMessage = 'Resource not found';
      } else if (error.status === 500) {
        errorMessage = 'Internal server error';
      } else {
        errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
      }
    }
    
    return throwError(() => new Error(errorMessage));
  }
}
